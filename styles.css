/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Prevent horizontal scroll on all elements */
*,
*::before,
*::after {
  max-width: 100%;
  box-sizing: border-box;
}

/* Ensure no element causes horizontal overflow - only essential elements */
.container,
.main-content {
  max-width: 100%;
}

/* Ensure all images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Prevent text overflow */
.section-title,
.main-title,
.arabic-title,
.english-subtitle {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Scroll Animation Base Styles */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Immediate visibility for critical content */
.section.active .animate-title,
.section.active .animate-text {
  animation: quickFadeIn 0.8s ease-out forwards;
}

@keyframes quickFadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent layout shift during animations */
.animate-title,
.animate-text,
.animate-card,
.animate-image,
.animate-slide-left,
.animate-slide-right,
.animate-scale,
.animate-stagger {
  will-change: transform, opacity;
}

/* Ensure content is accessible even without animations */
@media (prefers-reduced-motion: reduce) {
  .animate-on-scroll,
  .animate-fade-in,
  .animate-slide-left,
  .animate-slide-right,
  .animate-scale,
  .animate-stagger,
  .animate-card,
  .animate-image,
  .animate-title,
  .animate-text {
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
  }
}

.animate-fade-in {
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
}

.animate-fade-in.animate-in {
  opacity: 1;
}

.animate-slide-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-left.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.animate-slide-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-right.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.animate-scale {
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale.animate-in {
  opacity: 1;
  transform: scale(1);
}

.animate-stagger {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-stagger.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Stagger Animation Delays */
.animate-delay-1 {
  transition-delay: 0.1s;
}
.animate-delay-2 {
  transition-delay: 0.2s;
}
.animate-delay-3 {
  transition-delay: 0.3s;
}
.animate-delay-4 {
  transition-delay: 0.4s;
}
.animate-delay-5 {
  transition-delay: 0.5s;
}
.animate-delay-6 {
  transition-delay: 0.6s;
}

/* Enhanced Animation Classes */
.animate-float {
  animation: floatAnimation 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Hover Effects for Interactive Elements */
.sector-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.objective-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Special Animation Classes for Different Components */
.animate-card {
  opacity: 0;
  transform: translateY(20px) scale(0.98);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-card.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.animate-image {
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-image.animate-in {
  opacity: 1;
  transform: scale(1);
}

.animate-title {
  opacity: 0;
  transform: translateY(15px);
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-title.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.animate-text {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-text.animate-in {
  opacity: 1;
  transform: translateY(0);
}

html {
  overflow-x: hidden;
  width: 100%;
}

body {
  font-family: "Cairo", "Inter", sans-serif;
  background: linear-gradient(135deg, #3c2f6a 0%, #222147 100%);
  min-height: 100vh;
  color: white;
  overflow-x: hidden;
  position: relative;
  width: 100%;
  max-width: 100vw;
}

/* Prevent any element from causing horizontal scroll */
* {
  max-width: 100%;
}

*:not(.libya-map):not(.final-reservation-section::before) {
  box-sizing: border-box;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
  z-index: 0;
}

/* Libya Map Background */
.libya-map {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: min(600px, 90vw);
  height: min(400px, 60vh);
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.4) 2px,
    transparent 2px
  );
  background-size: 12px 12px;
  opacity: 0.5;
  z-index: 1;
  animation: pulse 4s ease-in-out infinite;
  clip-path: polygon(
    15% 20%,
    20% 15%,
    25% 12%,
    30% 10%,
    40% 8%,
    50% 10%,
    60% 12%,
    70% 15%,
    80% 20%,
    85% 30%,
    90% 40%,
    92% 50%,
    90% 60%,
    85% 70%,
    80% 80%,
    70% 85%,
    60% 88%,
    50% 90%,
    40% 88%,
    30% 85%,
    20% 80%,
    15% 70%,
    10% 60%,
    8% 50%,
    10% 40%,
    12% 30%,
    15% 20%
  );
}

.libya-map::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.6) 1px,
    transparent 1px
  );
  background-size: 8px 8px;
  background-position: 4px 4px;
  opacity: 0.7;
}

/* Container */
.container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 40px 20px;
  width: 100%;
  max-width: 100vw;
  animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

/* Additional Animation Keyframes */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* Enhanced Animation Keyframes for Beautiful Effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoomInRotate {
  from {
    opacity: 0;
    transform: scale(0.5) rotate(-5deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
  }
}

@keyframes floatAnimation {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Mobile-safe animation keyframes */
@keyframes mobileFloat {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes mobilePulse {
  0%,
  100% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
  }
  50% {
    opacity: 0.9;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
  }
}

@keyframes mobileShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Mobile Navigation Header - Hidden by default */
.mobile-nav-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(74, 38, 107, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 20px;
  z-index: 1000;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Ensure mobile header is only shown on mobile devices */
@media (min-width: 769px) {
  .mobile-nav-header {
    display: none !important;
  }

  body {
    padding-top: 0 !important;
  }
}

.mobile-logo {
  display: flex;
  align-items: center;
}

.mobile-logo-img {
  height: 40px;
  width: auto;
}

/* Hamburger Menu Button */
.hamburger-menu {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  transition: all 0.3s ease;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: #ffd700;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-menu.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-menu.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation */
.navigation {
  margin-bottom: 40px;
  z-index: 10;
  position: relative;
}

.nav-list {
  display: flex;
  justify-content: center;
  list-style: none;
  gap: 20px;
  flex-wrap: wrap;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-list li {
  flex: 0 1 calc(25% - 15px); /* 4 items per row on large screens */
  min-width: min(250px, 100%);
  display: flex;
  justify-content: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.1s ease; /* Much faster transition */
  font-weight: 500;
  font-size: 1rem;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: min(120px, 100%);
  cursor: pointer;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  padding: 0 15px;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.9);
  color: #4a266b;
  border-color: white;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px 0;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

/* Logo Styling */
.logo-container {
  flex: 1;
}

.logo-placeholder {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: flex-start;
}

.logo-icon {
  width: 230px;
  height: 160px;
  flex-shrink: 0;
}

.logo-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.logo-text-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffd700;
  margin: 0;
  line-height: 1.2;
}

.logo-subtitle {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Language Toggle */
.language-toggle {
  display: flex;
  gap: 8px;
  align-items: center;
}

.lang-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 25px;
  height: 40px;
  cursor: pointer;
  transition: all 0.1s ease; /* Much faster transition */
  font-weight: 500;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lang-btn:hover:not(.active) {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.lang-btn.active:hover {
  background: #ffed4e;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.5);
}

.logo-section {
  max-width: 800px;
  margin: 0 auto;
}

.arabic-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.english-subtitle {
  font-size: 1.5rem;
  font-weight: 400;
  opacity: 0.9;
  font-family: "Inter", sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content {
  flex: 1;
  position: relative;
}

.section {
  display: flex;
  min-height: auto;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section:last-child {
  border-bottom: none;
}

.content-wrapper {
  max-width: 1000px;
  text-align: center;
  padding: 0 20px;
  margin: 0 auto;
}

.main-title {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.description-text {
  font-size: 1.3rem;
  line-height: 1.8;
  font-weight: 400;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.description-text p {
  margin-bottom: 25px;
}

.description-text p:last-child {
  margin-bottom: 0;
}

/* Section Titles */
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  color: white;
}

/* Vision and Message Content */
.vision-content,
.message-content {
  max-width: 800px;
  margin: 0 auto;
}

.vision-text,
.message-text {
  font-size: 1.4rem;
  line-height: 1.8;
  font-weight: 400;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Objectives Grid */
.objectives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.objective-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.objective-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.objective-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.objective-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Companies Section */
.companies-content {
  max-width: 800px;
  margin: 0 auto;
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.company-category {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.company-category:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.category-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.category-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.companies-highlight {
  text-align: center;
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
}

.highlight-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0;
}

/* Audience Section */
.audience-content {
  max-width: 900px;
  margin: 0 auto;
}

.audience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 50px;
}

.audience-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.audience-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.audience-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0;
  line-height: 1.5;
}

.exhibitors-section {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.exhibitors-description {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 20px 0 0 0;
}

/* Visitors Section */
.visitors-section {
  margin-top: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(10px);
}

.visitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.visitor-category {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.visitor-category:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.visitor-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.visitor-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 0;
}

/* Industrial Sectors Section */
.sectors-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 40px;
  justify-content: center;
  align-items: stretch; /* Make all cards same height */
}

.sector-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  height: 300px;
  flex: 0 1 calc(25% - 15px); /* 4 items per row on large screens */
  min-width: 280px;
  max-width: 400px;
  position: relative;
}

.sector-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.sector-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

/* Exhibition Design Section */
.design-content {
  margin-top: 40px;
}

.design-section {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.design-section:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 215, 0, 0.5);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.design-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffd700;
  margin-bottom: 25px;
  text-align: center;
  position: relative;
}

.design-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffa500);
  border-radius: 2px;
}

.design-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.design-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.design-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #ffd700, #ffa500);
  border-radius: 0 2px 2px 0;
}

.design-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateX(5px);
}

.design-subtitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.6;
  margin: 0;
  padding-left: 15px;
}

.sector-content {
  padding: 25px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

.sector-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-top: 35px;
  line-height: 1.3;
}

/* Terms and Conditions Section */
.terms-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.terms-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.terms-section:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

.terms-title {
  color: #ffd700;
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 15px;
  text-align: center;
  position: relative;
}

.terms-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  border-radius: 2px;
}

.terms-item {
  margin-top: 15px;
}

.terms-text {
  color: #ffffff;
  font-size: 1rem;
  line-height: 1.8;
  margin: 0;
  text-align: justify;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  border-right: 4px solid #ffd700;
}

/* Terms Table Styles */
.pricing-table {
  margin: 15px 0;
  overflow-x: auto;
}

.terms-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.terms-table th,
.terms-table td {
  padding: 12px 15px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.terms-table th {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  font-weight: 700;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.terms-table td {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
}

.terms-table tbody tr:hover {
  background: rgba(255, 255, 255, 0.08);
}

.terms-table tbody tr:last-child td {
  border-bottom: none;
}

/* Reservation Navigation Links */
.nav-reservation-section .reservation-link {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #4a266b !important;
  font-weight: 700;
  border-radius: 25px;
  padding: 8px 16px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.nav-reservation-section .reservation-link:hover {
  background: linear-gradient(135deg, #ffed4e, #ffd700);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.guest-reservation {
  background: linear-gradient(135deg, #4caf50, #66bb6a) !important;
  color: white !important;
}

.guest-reservation:hover {
  background: linear-gradient(135deg, #66bb6a, #4caf50) !important;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5);
}

.company-reservation {
  background: linear-gradient(135deg, #2196f3, #42a5f5) !important;
  color: white !important;
}

.company-reservation:hover {
  background: linear-gradient(135deg, #42a5f5, #2196f3) !important;
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.5);
}

/* Main Reservation CTA Section */
.reservation-cta-section {
  margin-top: 40px;
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-title {
  color: #ffd700;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.reservation-buttons {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25px 35px;
  border-radius: 20px;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 200px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.guest-cta {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
}

.guest-cta:hover {
  background: linear-gradient(135deg, #66bb6a, #81c784);
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(76, 175, 80, 0.4);
}

.company-cta {
  background: linear-gradient(135deg, #2196f3, #42a5f5);
  color: white;
}

.company-cta:hover {
  background: linear-gradient(135deg, #42a5f5, #64b5f6);
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(33, 150, 243, 0.4);
}

.cta-icon {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.cta-text {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.cta-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
}

/* Final Reservation Section */
.final-reservation-section {
  margin-top: 50px;
  padding: 40px;
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.1),
    rgba(255, 215, 0, 0.05)
  );
  border-radius: 25px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.final-reservation-section::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  overflow: hidden;
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.1) 0%,
    transparent 70%
  );
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.final-cta-container {
  position: relative;
  z-index: 2;
}

.final-cta-title {
  color: #ffd700;
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.final-cta-description {
  color: #ffffff;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
}

.final-reservation-buttons {
  display: flex;
  gap: 25px;
  justify-content: center;
  flex-wrap: wrap;
}

.final-cta-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 18px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 180px;
  justify-content: center;
}

.final-cta-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.final-cta-button:hover::after {
  width: 300px;
  height: 300px;
}

.guest-final-cta {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
}

.guest-final-cta:hover {
  background: linear-gradient(135deg, #66bb6a, #81c784);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.5);
}

.company-final-cta {
  background: linear-gradient(135deg, #2196f3, #42a5f5);
  color: white;
}

.company-final-cta:hover {
  background: linear-gradient(135deg, #42a5f5, #64b5f6);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(33, 150, 243, 0.5);
}

.final-cta-icon {
  font-size: 1.5rem;
  z-index: 2;
  position: relative;
}

.final-cta-text {
  z-index: 2;
  position: relative;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .header,
[dir="rtl"] .content-wrapper {
  text-align: center;
}

[dir="ltr"] {
  font-family: "Inter", "Cairo", sans-serif;
}

[dir="rtl"] {
  font-family: "Cairo", "Inter", sans-serif;
}

/* Responsive Design */

/* Extra Large Desktop - 4 columns for 12 sectors */
@media (min-width: 1600px) {
  .sectors-grid {
    gap: 25px;
  }

  .sector-card {
    flex: 0 1 calc(25% - 19px); /* 4 items per row */
    min-width: 300px;
  }
}

/* Large Desktop - 3 columns for 12 sectors */
@media (min-width: 1200px) and (max-width: 1599px) {
  .sectors-grid {
    gap: 25px;
  }

  .sector-card {
    flex: 0 1 calc(33.333% - 17px); /* 3 items per row */
    min-width: 280px;
  }
}

/* Medium Desktop - 2 columns for 12 sectors */
@media (min-width: 900px) and (max-width: 1199px) {
  .sectors-grid {
    gap: 20px;
  }

  .sector-card {
    flex: 0 1 calc(50% - 10px); /* 2 items per row */
    min-width: 300px;
  }
}

/* Tablet landscape and small desktop */
@media (max-width: 1024px) and (min-width: 769px) {
  .libya-map {
    width: 450px;
    height: 320px;
  }

  .arabic-title {
    font-size: 2.2rem;
  }

  .main-title {
    font-size: 2.4rem;
  }

  .container {
    padding: 30px 20px;
  }

  .nav-list {
    gap: 15px;
  }

  .nav-list li {
    flex: 0 1 calc(50% - 10px);
    min-width: min(200px, 100%);
  }

  .nav-link {
    padding: 12px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  /* Show mobile navigation header */
  .mobile-nav-header {
    display: flex !important;
  }

  /* Add top padding to body to account for fixed header */
  body {
    padding-top: 70px;
  }

  /* Hide desktop navigation by default on mobile */
  .navigation {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    width: 100%;
    background: rgba(74, 38, 107, 0.98);
    backdrop-filter: blur(15px);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    z-index: 999;
    margin-bottom: 0;
  }

  /* Show navigation when active */
  .navigation.active {
    max-height: calc(100vh - 70px);
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    -webkit-overflow-scrolling: touch;
  }

  /* Hide scrollbar in mobile navigation */
  .navigation.active::-webkit-scrollbar {
    display: none;
  }

  .navigation.active {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Mobile navigation list styling */
  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 15px;
    margin: 0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .nav-list li {
    flex: none;
    min-width: auto;
    max-width: none;
    width: 100%;
    margin-bottom: 8px;
  }

  .nav-list li:last-child {
    margin-bottom: 0;
  }

  .nav-link {
    display: block;
    width: 100%;
    padding: 12px 15px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-size: 0.9rem;
    min-width: auto;
    height: auto;
    transition: all 0.3s ease;
    box-sizing: border-box;
  }

  .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  .nav-reservation-section .reservation-link {
    background: linear-gradient(135deg, #ffd700, #ffed4e) !important;
    color: #4a266b !important;
    font-weight: 700;
    margin-top: 5px;
    border: 2px solid rgba(255, 215, 0, 0.5) !important;
    padding: 12px 15px !important;
    font-size: 0.9rem !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .nav-reservation-section .reservation-link:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
  }

  /* Mobile-safe animations - only opacity and very subtle effects */
  .animate-float {
    animation: mobileFloat 4s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: mobilePulse 3s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    background-size: 200% 100%;
    animation: mobileShimmer 3s infinite;
  }

  /* Mobile-safe scroll animations - only opacity */
  .animate-on-scroll,
  .animate-fade-in,
  .animate-slide-left,
  .animate-slide-right,
  .animate-scale,
  .animate-stagger,
  .animate-card,
  .animate-image,
  .animate-title,
  .animate-text {
    opacity: 0;
    transition: opacity 0.6s ease !important;
    transform: none !important;
  }

  .animate-on-scroll.animate-in,
  .animate-fade-in.animate-in,
  .animate-slide-left.animate-in,
  .animate-slide-right.animate-in,
  .animate-scale.animate-in,
  .animate-stagger.animate-in,
  .animate-card.animate-in,
  .animate-image.animate-in,
  .animate-title.animate-in,
  .animate-text.animate-in {
    opacity: 1;
    transform: none !important;
  }

  /* Mobile-safe AOS animations - only opacity */
  [data-aos] {
    opacity: 0 !important;
    transform: none !important;
    transition: opacity 0.6s ease !important;
  }

  [data-aos].aos-animate {
    opacity: 1 !important;
    transform: none !important;
  }

  /* Disable hover transforms on mobile but keep other effects */
  .sector-card:hover,
  .objective-item:hover,
  .cta-button:hover,
  .company-category:hover,
  .audience-item:hover,
  .visitor-category:hover,
  .design-section:hover,
  .terms-section:hover,
  .image-placeholder:hover,
  .nav-link:hover,
  .lang-btn:hover {
    transform: none !important;
  }

  .arabic-title {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  .english-subtitle {
    font-size: 1.1rem;
  }

  .main-title {
    font-size: 1.8rem;
    line-height: 1.3;
    margin-bottom: 30px;
  }

  .description-text {
    font-size: 1rem;
    line-height: 1.7;
  }

  .libya-map {
    width: 350px;
    height: 250px;
  }

  .container {
    padding: 20px 15px;
  }

  .header {
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 30px;
  }

  .vision-text,
  .message-text {
    font-size: 1.2rem;
    line-height: 1.7;
  }

  .objectives-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .objective-item {
    padding: 20px;
  }

  .objective-title {
    font-size: 1.2rem;
  }

  .objective-description {
    font-size: 0.95rem;
  }

  .companies-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .company-category {
    padding: 20px;
  }

  .category-title {
    font-size: 1.2rem;
  }

  .highlight-title {
    font-size: 1.7rem;
  }

  .audience-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .audience-item {
    padding: 20px;
  }

  .audience-title {
    font-size: 1.1rem;
  }

  .exhibitors-description {
    font-size: 1.1rem;
  }

  .visitors-section {
    margin-top: 30px;
    padding: 30px;
  }

  .visitors-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .visitor-category {
    padding: 20px;
  }

  .visitor-title {
    font-size: 1.2rem;
  }

  .visitor-description {
    font-size: 0.95rem;
  }

  .sectors-grid {
    gap: 25px;
    margin-top: 30px;
  }

  .sector-card {
    flex: 0 1 calc(50% - 12px); /* 2 items per row on tablet */
    min-width: 250px;
  }

  .sector-card {
    height: 280px;
  }

  .sector-image {
    height: 180px;
  }

  .sector-content {
    height: 100px;
    padding: 20px;
  }

  .sector-title {
    font-size: 1.3rem;
  }

  .design-section {
    padding: 25px;
    margin-bottom: 25px;
  }

  .design-title {
    font-size: 1.6rem;
  }

  .design-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .arabic-title {
    font-size: 1.6rem;
  }

  .english-subtitle {
    font-size: 1rem;
  }

  .main-title {
    font-size: 1.5rem;
  }

  .description-text {
    font-size: 1rem;
    line-height: 1.6;
  }

  .libya-map {
    width: 280px;
    height: 180px;
  }

  /* Improve mobile header spacing */
  .mobile-nav-header {
    padding: 12px 15px;
  }

  .mobile-logo-img {
    height: 35px;
  }

  /* Better mobile navigation */
  .nav-link {
    padding: 12px 15px;
    font-size: 0.9rem;
  }

  /* Improve button responsiveness */
  .reservation-buttons {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .cta-button {
    min-width: 250px;
    max-width: 300px;
  }

  /* Better table responsiveness */
  .terms-table th,
  .terms-table td {
    padding: 8px 10px;
    font-size: 0.85rem;
  }

  /* Improve sector cards on very small screens */
  .sectors-grid {
    gap: 15px;
  }

  .sector-card {
    flex: 0 1 100%; /* 1 item per row on small mobile */
    min-width: 250px;
  }

  .sector-card {
    height: 250px;
  }

  /* Ensure proper layout on small screens */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Mobile-safe animations - only opacity-based */
  .animate-float {
    animation: mobileFloat 4s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: mobilePulse 3s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    background-size: 200% 100%;
    animation: mobileShimmer 3s infinite;
  }

  /* Ensure AOS animations work but are safe */
  [data-aos] {
    opacity: 0;
    transition: opacity 0.6s ease;
  }

  [data-aos].aos-animate {
    opacity: 1;
  }

  .lang-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
}

.section-title {
  font-size: 1.7rem;
  margin-bottom: 25px;
}

.vision-text,
.message-text {
  font-size: 1.1rem;
  line-height: 1.6;
}

.objective-item {
  padding: 15px;
}

.objective-title {
  font-size: 1.1rem;
  margin-bottom: 10px;
}

.objective-description {
  font-size: 0.9rem;
  line-height: 1.5;
}

.objectives-grid {
  margin-top: 25px;
  gap: 15px;
}

.container {
  padding: 15px 10px;
}

.content-wrapper {
  padding: 0 10px;
}

.companies-grid {
  grid-template-columns: 1fr;
  gap: 15px;
  margin-bottom: 30px;
}

.company-category {
  padding: 15px;
}

.category-title {
  font-size: 1.1rem;
  margin-bottom: 10px;
}

.category-description {
  font-size: 0.9rem;
}

.companies-highlight {
  padding: 20px;
}

.highlight-title {
  font-size: 1.5rem;
}

.audience-grid {
  grid-template-columns: 1fr;
  gap: 15px;
  margin-bottom: 30px;
}

.audience-item {
  padding: 15px;
}

.audience-title {
  font-size: 1rem;
}

.exhibitors-section {
  padding: 20px;
}

.exhibitors-description {
  font-size: 1rem;
  line-height: 1.6;
}

.visitors-section {
  margin-top: 20px;
  padding: 20px;
}

.visitors-grid {
  grid-template-columns: 1fr;
  gap: 15px;
  margin-top: 20px;
}

.visitor-category {
  padding: 15px;
}

.visitor-title {
  font-size: 1.1rem;
  margin-bottom: 10px;
}

.visitor-description {
  font-size: 0.9rem;
  line-height: 1.5;
}

.sectors-grid {
  gap: 20px;
  margin-top: 25px;
}

.sector-card {
  flex: 0 1 100%; /* 1 item per row on mobile */
  min-width: 250px;
}

.sector-card {
  height: 250px;
}

.sector-image {
  height: 150px;
}

.sector-content {
  height: 100px;
  padding: 15px;
}

.sector-title {
  font-size: 1.1rem;
  line-height: 1.2;
}

.design-section {
  padding: 20px;
  margin-bottom: 20px;
}

.design-title {
  font-size: 1.4rem;
}

.design-subtitle {
  font-size: 0.95rem;
  padding-left: 10px;
}

.design-item {
  padding: 15px;
}

/* Big Image Placeholders */
.big-image-section {
  padding: 60px 0;
  margin: 40px 0;
}

.big-image-container {
  margin: 0 auto;
}

.image-placeholder {
  position: relative;
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 3px dashed rgba(255, 215, 0, 0.4);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  border-color: rgba(255, 215, 0, 0.6);
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(74, 38, 107, 0.8),
    rgba(65, 179, 211, 0.6)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.image-placeholder:hover .image-overlay {
  opacity: 1;
}

.image-content {
  text-align: center;
  padding: 20px;
}

.image-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ffd700;
  margin-bottom: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.image-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.placeholder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-svg {
  width: 300px;
  height: 180px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.image-placeholder:hover .placeholder-svg {
  opacity: 0.3;
  transform: scale(1.1);
}

/* Beautiful Footer */
.footer {
  background: linear-gradient(
    135deg,
    rgba(74, 38, 107, 0.95),
    rgba(65, 179, 211, 0.85)
  );
  backdrop-filter: blur(10px);
  border-top: 2px solid rgba(255, 215, 0, 0.3);
  margin-top: 15px;
  padding: 15px;
  position: relative;
  border-radius: 20px;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,215,0,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,215,0,0.1)"/><circle cx="60" cy="70" r="2.5" fill="rgba(255,215,0,0.1)"/><circle cx="30" cy="80" r="1" fill="rgba(255,215,0,0.1)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

/* Footer Logo */
.footer-info {
  align-items: flex-start;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.footer-logo-icon {
  width: 230px;
  height: 160px;
  flex-shrink: 0;
}

.footer-logo-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
}

.footer-logo-text {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffd700;
  margin: 0 0 5px 0;
  line-height: 1.2;
}

.footer-subtitle {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.3;
}

/* Footer Section Titles */
.footer-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 15px;
  position: relative;
}

.footer-section-title::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #ffd700, transparent);
}

/* Social Media Links */
.social-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 215, 0, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.social-link:hover::before {
  left: 100%;
}

.social-link:hover {
  background: rgba(255, 215, 0, 0.15);
  border-color: rgba(255, 215, 0, 0.4);
  transform: translateX(5px);
}

.social-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.facebook:hover {
  color: #1877f2;
}

.linkedin:hover {
  color: #0a66c2;
}

.social-text {
  font-weight: 500;
  font-size: 0.95rem;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  line-height: 1.4;
  margin: 0;
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-item:last-child {
  border-bottom: none;
}

/* Phone number should always display left-to-right */
.phone-number {
  direction: ltr !important;
  text-align: left !important;
}

/* In RTL mode, align phone number to the right but keep LTR direction */
[dir="rtl"] .phone-number {
  text-align: right !important;
  direction: ltr !important;
}

/* Footer Map Section */
.footer-map {
  align-items: flex-start;
}

.map-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.map-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.map-container iframe {
  display: block;
  width: 100%;
  height: 200px;
  border: none;
  border-radius: 8px;
}

/* Footer Bottom */
.footer-bottom {
  position: relative;
  z-index: 1;
}

.footer-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 215, 0, 0.5),
    transparent
  );
  margin: 5px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding-bottom: 10px;
}

.copyright {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
}

.footer-tagline {
  color: #ffd700;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
  font-style: italic;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer {
    padding: 25px 0 10px 0;
    margin-top: 30px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 25px;
    margin-bottom: 20px;
    text-align: center;
  }

  .map-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .footer-logo {
    justify-content: center;
  }

  .logo-text-container {
    align-items: center;
  }

  .footer-section-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .header-top {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .logo-placeholder {
    justify-content: center;
  }

  .logo-text-container {
    align-items: center;
  }

  .image-placeholder {
    height: 300px;
  }

  .placeholder-svg {
    width: 250px;
    height: 150px;
  }

  .image-title {
    font-size: 1.5rem;
  }

  .image-description {
    font-size: 1rem;
  }

  /* Terms Section Responsive */
  .terms-content {
    gap: 20px;
  }

  .terms-section {
    padding: 20px;
  }

  .terms-title {
    font-size: 1.2rem;
  }

  .terms-text {
    font-size: 0.95rem;
    padding: 12px;
    text-align: right;
    line-height: 1.7;
  }

  /* Reservation Sections Responsive */
  .reservation-cta-section {
    margin-top: 30px;
    padding: 20px;
  }

  .cta-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
  }

  .reservation-buttons {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  .cta-button {
    min-width: 250px;
    padding: 20px 25px;
  }

  .cta-icon {
    font-size: 2rem;
  }

  .cta-text {
    font-size: 1.1rem;
  }

  .final-reservation-section {
    margin-top: 30px;
    padding: 25px 15px;
  }

  .final-cta-title {
    font-size: 1.6rem;
    margin-bottom: 12px;
  }

  .final-cta-description {
    font-size: 1rem;
    margin-bottom: 25px;
  }

  .final-reservation-buttons {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .final-cta-button {
    min-width: 200px;
    padding: 15px 25px;
    font-size: 1rem;
  }

  .nav-reservation-section .reservation-link {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* ===== SPONSORS SECTION ===== */
.sponsors-title-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.sponsors-main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.sponsors-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 215, 0, 0.9);
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.sponsors-placeholder {
  height: 400px;
  position: relative;
  overflow: hidden;
}

.sponsors-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 360px;
  backdrop-filter: blur(10px);
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  z-index: 5;
}

/* Swiper container for clients */
.clients-swiper {
  width: 100%;
  height: 100%;
}

.clients-swiper .swiper-wrapper {
  transition-timing-function: linear !important;
}

.clients-swiper .swiper-slide {
  width: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.clients-swiper .swiper-slide img {
  max-width: 280px;
  max-height: 100%;
  object-fit: contain;
  filter: brightness(0.9) contrast(1.1);
  transition: all 0.5s ease;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.clients-swiper .swiper-slide:hover img {
  filter: brightness(1.1) contrast(1);
  transform: scale(1.1) translateZ(0);
}

/* Additional performance optimizations */
.sponsors-animation * {
  box-sizing: border-box;
}

/* Ensure smooth animation on all devices */
@media (prefers-reduced-motion: no-preference) {
  .sponsors-track {
    animation-duration: 15s;
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .sponsors-track {
    animation: none;
  }

  .sponsors-track:hover {
    animation: none;
  }
}

/* Responsive Design for Sponsors */
@media (max-width: 768px) {
  .sponsors-title-section {
    margin-bottom: 20px;
    padding: 0 15px;
  }

  .sponsors-main-title {
    font-size: 2rem;
    margin-bottom: 10px;
  }

  .sponsors-subtitle {
    font-size: 1.1rem;
  }

  .sponsors-placeholder {
    height: 300px;
  }

  .sponsors-animation {
    height: 260px;
  }

  .clients-swiper .swiper-slide img {
    max-width: 200px;
  }
}

/* ===== PARTICIPANTS SECTION ===== */
.participants-title-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.participants-main-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.participants-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 215, 0, 0.9);
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.participants-placeholder {
  height: 400px;
  position: relative;
  overflow: hidden;
}

.participants-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 360px;
  display: flex;
  align-items: center;
  overflow: hidden;
  z-index: 5;
}

/* Swiper container for participants */
.participants-swiper {
  width: 100%;
  height: 100%;
}

.participants-swiper .swiper-wrapper {
  transition-timing-function: linear !important;
}

.participants-swiper .swiper-slide {
  width: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.participants-swiper .swiper-slide img {
  max-width: 280px;
  max-height: 100%;
  object-fit: contain;
  filter: brightness(0.9) contrast(1.1);
  transition: all 0.5s ease;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.participants-swiper .swiper-slide:hover img {
  filter: brightness(1.1) contrast(1);
  transform: scale(1.1) translateZ(0);
}

/* Additional performance optimizations */
.participants-animation * {
  box-sizing: border-box;
}

/* Responsive Design for Participants */
@media (max-width: 768px) {
  .participants-title-section {
    margin-bottom: 20px;
    padding: 0 15px;
  }

  .participants-main-title {
    font-size: 2rem;
    margin-bottom: 10px;
  }

  .participants-subtitle {
    font-size: 1.1rem;
  }

  .participants-placeholder {
    height: 300px;
  }

  .participants-animation {
    height: 260px;
  }

  .participants-swiper .swiper-slide img {
    max-width: 200px;
  }
}

/* Extra small screens (320px and below) */
@media (max-width: 320px) {
  .container {
    padding: 15px 10px;
  }

  .mobile-nav-header {
    padding: 10px 12px;
  }

  .mobile-logo-img {
    height: 30px;
  }

  .hamburger-menu {
    width: 25px;
    height: 25px;
  }

  .hamburger-line {
    width: 20px;
    height: 2px;
  }

  .arabic-title {
    font-size: 1.4rem;
  }

  .main-title {
    font-size: 1.3rem;
  }

  .libya-map {
    width: 250px;
    height: 160px;
  }

  .nav-link {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .cta-button {
    min-width: 220px;
    padding: 12px 20px;
  }

  .terms-table th,
  .terms-table td {
    padding: 6px 8px;
    font-size: 0.8rem;
  }

  .sector-card {
    height: 220px;
  }

  .sector-title {
    font-size: 1rem;
  }
}
